import { useTranslations } from "next-intl";
import Text from "@/styles/text-styles";
import MultilanguageMetaFields from "./multilanguage-meta-fields";
import KeywordsSection from "./keywords";
import { Language } from "../../types/multilanguage-seo";

interface MultilanguageSeoContentProps {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
  activeLanguage: Language;
  changeMetaTitle: (title: string) => void;
  changeMetaDescription: (description: string) => void;
  addNewKeyword: (keyword: string) => void;
  removeKeyword: (keyword: string) => void;
}

export default function MultilanguageSeoContent({
  metaTitle,
  metaDescription,
  keywords,
  activeLanguage,
  changeMetaTitle,
  changeMetaDescription,
  addNewKeyword,
  removeKeyword,
}: MultilanguageSeoContentProps) {
  const uploadContent = useTranslations(
    "shared.forms.upload.seoLabels.seoSettings"
  );

  return (
    <div className="space-y-7">
      <Text textStyle="TS5" className="font-bold text-black">
        {uploadContent("title")}
      </Text>

      <div className="space-y-5">
        <div
          style={{
            display: activeLanguage === "arabic" ? "block" : "none",
          }}
        >
          <div className="space-y-5">
            <KeywordsSection
              keywords={keywords}
              addNewKeyword={addNewKeyword}
              removeKeyword={removeKeyword}
              activeLanguage="arabic"
              multilanguage={true}
            />
            <MultilanguageMetaFields
              multilanguage={true}
              activeLanguage="arabic"
              metaTitle={metaTitle}
              metaDescription={metaDescription}
              onTitleChange={changeMetaTitle}
              onDescriptionChange={changeMetaDescription}
            />
          </div>
        </div>

        <div
          style={{
            display: activeLanguage === "french" ? "block" : "none",
          }}
        >
          <div className="space-y-5">
            <KeywordsSection
              keywords={keywords}
              addNewKeyword={addNewKeyword}
              removeKeyword={removeKeyword}
              activeLanguage="french"
              multilanguage={true}
            />
            <MultilanguageMetaFields
              multilanguage={true}
              activeLanguage="french"
              metaTitle={metaTitle}
              metaDescription={metaDescription}
              onTitleChange={changeMetaTitle}
              onDescriptionChange={changeMetaDescription}
            />
          </div>
        </div>

        <div
          style={{
            display: activeLanguage === "english" ? "block" : "none",
          }}
        >
          <div className="space-y-5">
            <KeywordsSection
              keywords={keywords}
              addNewKeyword={addNewKeyword}
              removeKeyword={removeKeyword}
              activeLanguage="english"
              multilanguage={true}
            />
            <MultilanguageMetaFields
              multilanguage={true}
              activeLanguage="english"
              metaTitle={metaTitle}
              metaDescription={metaDescription}
              onTitleChange={changeMetaTitle}
              onDescriptionChange={changeMetaDescription}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
