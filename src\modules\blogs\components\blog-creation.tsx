"use client";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import FormSubmission from "@/modules/catalog/components/form-submission";
import { useRouter } from "next/navigation";
import useMultilanguageBlogCreation from "@/modules/blogs/hooks/use-multilanguage-blog-creation";
import useSeoMetaData from "@/modules/seo/hooks/use-seo-meta-data";
import MultilanguageSeoContent from "@/modules/seo/components/seo-meta-content/multilanguage-seo-content";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import { useState } from "react";
import MultilanguageBlogFormFields from "./multilanguage-blog-form-fields";
import WarnInput from "@/components/input/warn-input";
import { Label } from "@/components/ui/label";
import ImageUpload from "@/modules/page-palette/components/image-upload";
import { Language } from "@/modules/seo/types/multilanguage-seo";
import LanguageTabs from "@/modules/catalog/components/brands/brand-upload/language-tabs";

export default function BlogCreation() {
  const t = useTranslations("BlogsManagement");
  const uploadContent = useTranslations("shared.forms.upload");

  const [activeLanguage, setActiveLanguage] = useState<Language>("french");

  const router = useRouter();
  const previousUrl = usePreviousUrl();

  const {
    metaTitle,
    metaDescription,
    keywords,
    handleMetaTitleChange,
    handleMetaDescriptionChange,
    addNewKeyword,
    removeKeyword,
    getMultilanguageMetaContent,
  } = useSeoMetaData();

  const { formRef, submitBlog, warning, isPending } =
    useMultilanguageBlogCreation(getMultilanguageMetaContent);

  const cancelSubmission = () => {
    if (previousUrl && previousUrl.startsWith("/blogs"))
      router.push(previousUrl);
    else router.push("/blogs");
  };

  return (
    <FormSubmission
      submit={uploadContent("save")}
      cancel={uploadContent("cancel")}
      isPending={isPending}
      onCancel={cancelSubmission}
      onSubmit={submitBlog}
    >
      <form ref={formRef} className="flex flex-col extraXL:flex-row gap-4">
        <div className="basis-[70%] regularL:order-1 order-2 flex flex-col gap-5">
          {/* Language Content Card */}
          <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-7">
            <LanguageTabs
              options={[
                { key: "arabic", value: uploadContent("languages.arabic") },
                { key: "french", value: uploadContent("languages.french") },
                { key: "english", value: uploadContent("languages.english") },
              ]}
              onSelect={(language) => {
                setActiveLanguage(language as Language);
              }}
              selectedValue={activeLanguage}
            />
            <Text textStyle="TS4" className="font-bold text-black">
              {t("blogContent")}
            </Text>
            <div className="text-red self-center">{warning}</div>

            <div className="w-full">
              <div
                style={{
                  display: activeLanguage === "arabic" ? "block" : "none",
                }}
              >
                <MultilanguageBlogFormFields
                  multilanguage={true}
                  language="arabic"
                />
              </div>

              <div
                style={{
                  display: activeLanguage === "french" ? "block" : "none",
                }}
              >
                <MultilanguageBlogFormFields
                  multilanguage={true}
                  language="french"
                />
              </div>

              <div
                style={{
                  display: activeLanguage === "english" ? "block" : "none",
                }}
              >
                <MultilanguageBlogFormFields
                  multilanguage={true}
                  language="english"
                />
              </div>
            </div>

            {/* Display Order */}
            <div className="flex flex-col space-y-2">
              <Label htmlFor="displayOrder">
                {uploadContent("blogLabels.displayOrder")}{" "}
                {uploadContent("optional")} :
              </Label>
              <WarnInput
                id="displayOrder"
                name="displayOrder"
                type="number"
                warning=""
                min={1}
              />
            </div>

            {/* Image Upload */}
            <div className="flex flex-col space-y-2">
              <Label>{uploadContent("blogLabels.image")}</Label>
              <ImageUpload name="image" />
            </div>
          </div>

          <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-7">
            <MultilanguageSeoContent
              metaTitle={metaTitle}
              metaDescription={metaDescription}
              keywords={keywords}
              activeLanguage={activeLanguage}
              changeMetaTitle={handleMetaTitleChange}
              changeMetaDescription={handleMetaDescriptionChange}
              addNewKeyword={addNewKeyword}
              removeKeyword={removeKeyword}
            />
          </div>
        </div>
      </form>
    </FormSubmission>
  );
}
