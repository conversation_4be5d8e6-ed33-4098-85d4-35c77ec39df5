"use client";
import WarnInput from "@/components/input/warn-input";
import Text from "@/styles/text-styles";
import FormSubmission from "../../form-submission";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import ItemsList from "../items-list";
import { Skeleton } from "@/components/ui/skeleton";
import { DashboardListsContainerSkeleton } from "@/components/dashbord-lists-container";
import { Label } from "@/components/ui/label";
import useEditableProduct from "@/modules/catalog/hooks/products/use-editable-product";
import { useEffect, useState } from "react";
import ListPicker from "@/components/list-picker";
import useBrands from "@/modules/catalog/hooks/brands/use-brands";
import { Switch } from "@/components/ui/switch";
import useSeoMetaData from "@/modules/seo/hooks/use-seo-meta-data";
import { usePreviousUrl } from "@/hooks/urls-management/use-previous-url";
import CategoriesSelection from "../../categories/categories-selection";
import useCategories from "@/modules/catalog/hooks/categories/use-categories";
import { disableScrollOnNumberInput } from "@/utils/number-input";
import useMultilanguageProductEdition from "@/modules/catalog/hooks/products/use-multilanguage-product-edition";
import LanguageTabs from "../../brands/brand-upload/language-tabs";
import MultilanguageProductFormFields from "./multilanguage-product-form-fields";
import MultilanguageSeoContent from "@/modules/seo/components/seo-meta-content/multilanguage-seo-content";

interface Props {
  productSlug: string;
}

export default function ProductEdition({ productSlug }: Props) {
  const uploadContent = useTranslations("shared.forms.upload");
  const t = useTranslations("ProductsManagement");
  const router = useRouter();
  const previousUrl = usePreviousUrl();

  const [selectedBrand, setSelectedBrand] = useState<string | undefined>(
    undefined
  );
  const [productVariationsIsDisplayed, setProductVariationsIsDisplayed] =
    useState(true);

  const {
    formRef,
    submitProduct,
    warning,
    isPending,
    activeLanguage,
    handleLanguageChange,
    formKey,
  } = useMultilanguageProductEdition(
    () => getMultilanguageMetaContent(),
    () => product?.id || "",
    () => {
      refetch();
    },
    (newSlug: string) => {
      const currentPath = window.location.pathname;
      const newPath = currentPath.replace(
        /\/products\/[^\/]+\/edit/,
        `/products/${newSlug}/edit`
      );
      router.replace(newPath);
    }
  );

  const {
    product,
    isLoading: productIsLoading,
    refetch,
  } = useEditableProduct({
    productSlug: productSlug || "",
    language: activeLanguage,
  });
  const { categories, categoriesAreLoading } = useCategories();
  const { brands, brandsAreLoading } = useBrands({});

  const [selectedCategoriesIds, setSelectedCategoriesIds] = useState<
    { id: string; slug: string }[]
  >([]);

  const {
    metaTitle,
    metaDescription,
    keywords,
    handleMetaTitleChange,
    handleMetaDescriptionChange,
    addNewKeyword,
    removeKeyword,
    getMultilanguageMetaContent,
  } = useSeoMetaData();

  useEffect(() => {
    if (product) setSelectedBrand(product?.brand?.id);
  }, [product]);

  const onCancelSubmission = () => {
    if (previousUrl && previousUrl.startsWith("/products")) {
      router.push(previousUrl);
    } else router.push("/products");
  };

  return !(productIsLoading || product === undefined) &&
    !(brandsAreLoading || brands === undefined) &&
    !(categoriesAreLoading || categories === undefined) ? (
    <div className="flex flex-col py-3">
      <FormSubmission
        cancel={uploadContent("cancel")}
        submit={uploadContent("save")}
        isPending={isPending}
        onCancel={onCancelSubmission}
        onSubmit={submitProduct}
      >
        <div className="p-3 bg-white flex flex-col space-y-6">
          <Text textStyle="TS4" className="font-bold text-black">
            {t("productInfo")}
          </Text>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <Text textStyle="TS6" className="text-yellow-800">
              {uploadContent("saveBeforeSwitchingNote")}
            </Text>
          </div>

          <form
            ref={formRef}
            key={formKey}
            className="flex flex-col extraXL:flex-row gap-4"
          >
            <div className="basis-[70%] regularL:order-1 order-2 flex flex-col gap-5">
              {/* Language Content Card */}
              <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-7">
                <Text textStyle="TS4" className="font-bold text-black">
                  {t("productContent")}
                </Text>
                <div className="text-red self-center">{warning}</div>

                <div className="w-full">
                  <LanguageTabs
                    options={[
                      { key: "arabic", value: t("languages.arabic") },
                      { key: "french", value: t("languages.french") },
                      { key: "english", value: t("languages.english") },
                    ]}
                    onSelect={handleLanguageChange}
                    selectedValue={activeLanguage}
                  />
                  <hr className="mb-4" />

                  <div
                    style={{
                      display: activeLanguage === "arabic" ? "block" : "none",
                    }}
                  >
                    <MultilanguageProductFormFields
                      multilanguage={true}
                      language="arabic"
                      initialName={product?.name || ""}
                      initialDescription={product?.description || ""}
                      initialDetails={product?.details || ""}
                    />
                  </div>

                  <div
                    style={{
                      display: activeLanguage === "french" ? "block" : "none",
                    }}
                  >
                    <MultilanguageProductFormFields
                      multilanguage={true}
                      language="french"
                      initialName={product?.name || ""}
                      initialDescription={product?.description || ""}
                      initialDetails={product?.details || ""}
                    />
                  </div>

                  <div
                    style={{
                      display: activeLanguage === "english" ? "block" : "none",
                    }}
                  >
                    <MultilanguageProductFormFields
                      multilanguage={true}
                      language="english"
                      initialName={product?.name || ""}
                      initialDescription={product?.description || ""}
                      initialDetails={product?.details || ""}
                    />
                  </div>
                </div>
              </div>

              {/* Product Settings Card */}
              <div className=" rounded-2xl border border-lightGray p-7 bg-white  space-y-7">
                <Text textStyle="TS4" className="font-bold text-black">
                  {t("productSettings")}
                </Text>

                {/* Brand */}
                <div>
                  <div className="w-full flex flex-col space-y-3">
                    <Label htmlFor="brandId">
                      {`${uploadContent(
                        "brandLabels.brandType"
                      )} ${uploadContent("optional")}`}
                    </Label>
                    <ListPicker
                      data={brands}
                      onChange={(id) => setSelectedBrand(id)}
                      selectedElementId={selectedBrand || ""}
                      className="w-full max-w-full"
                      dropdownClassName="L:w-[300px] shadow-md border"
                      pickedElementName={"brandId"}
                    />
                  </div>
                </div>

                <div className="w-full flex flex-col space-y-3">
                  <Label htmlFor="displayOrder">
                    {uploadContent("productLabels.displayOrder")}
                    {uploadContent("optional")}
                  </Label>
                  <WarnInput
                    name="displayOrder"
                    type="number"
                    onWheel={disableScrollOnNumberInput}
                    warning=""
                    placeholder={uploadContent("productLabels.displayOrder")}
                    value={
                      product.displayOrder === 0 ? "0" : product.displayOrder
                    }
                  />
                </div>

                <CategoriesSelection
                  setSelectedCategories={setSelectedCategoriesIds}
                  defaultSelectedCategoriesIds={product.categoryIds}
                  className="flex flex-col 2L:flex-col gap-5 L:gap-6 mt-6"
                />
                {selectedCategoriesIds.length > 0 ? (
                  selectedCategoriesIds.map((cat) => (
                    <input
                      key={cat.id}
                      name={"categoryIds"}
                      className="hidden"
                      value={cat.id}
                      readOnly
                    />
                  ))
                ) : (
                  <input
                    name={"categoryIds"}
                    className="hidden"
                    value={""}
                    readOnly
                  />
                )}
              </div>

              <div className=" rounded-2xl border border-lightGray p-7 bg-white  space-y-7">
                <Text textStyle="TS5" className="font-bold text-black">
                  {t("variations.title")}
                </Text>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="productVariationsIsDisplayed"
                    checked={productVariationsIsDisplayed}
                    onCheckedChange={setProductVariationsIsDisplayed}
                  />
                  <Label htmlFor="productVariationsIsDisplayed">
                    {t("variations.isDisplayVariation")}
                  </Label>
                </div>
                {productVariationsIsDisplayed && (
                  <>
                    <ItemsList
                      items={product.items}
                      product={{ id: product.id, slug: product.slug }}
                    />
                  </>
                )}
              </div>
            </div>
            <div className="basis-[30%] order-2 space-y-4">
              <div className="rounded-2xl border border-lightGray p-7 bg-white space-y-5">
                <MultilanguageSeoContent
                  metaTitle={metaTitle}
                  metaDescription={metaDescription}
                  keywords={keywords}
                  activeLanguage={activeLanguage as any}
                  changeMetaTitle={handleMetaTitleChange}
                  changeMetaDescription={handleMetaDescriptionChange}
                  addNewKeyword={addNewKeyword}
                  removeKeyword={removeKeyword}
                />
              </div>
            </div>
          </form>
        </div>
      </FormSubmission>
    </div>
  ) : (
    <div className="py-2 w-full flex XL:flex-row flex-col XL:gap-8 gap-4">
      <DashboardListsContainerSkeleton className="flex-1">
        {/* Product Information Section */}
        <div className="w-full flex flex-col space-y-4">
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* Code à barre */}
            <Skeleton className="h-9 w-full max-w-[500px]" />
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* Nom de produit */}
            <Skeleton className="h-9 w-full max-w-[500px]" />
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* catégorie mère */}
            <div className="flex gap-4">
              <Skeleton className="h-9 w-1/2" /> {/* Select product category */}
              <Skeleton className="h-9 w-1/2" /> {/* Select product category */}
            </div>
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* Description */}
            <Skeleton className="h-24 w-full max-w-[800px]" />
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* Quantity */}
            <Skeleton className="h-9 w-full max-w-[500px]" />
          </div>

          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* Product variations */}
            <div className="flex items-center gap-4">
              <Skeleton className="h-10 w-20" />
              {/* Display variations toggle */}
              <Skeleton className="h-10 w-24" /> {/* Delete Item button */}
              <Skeleton className="h-10 w-20" /> {/* Add Item button */}
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="flex flex-col items-center">
                <Skeleton className="h-32 w-full" />
                {/* Product variation image */}
                <Skeleton className="h-6 w-32 mt-2" /> {/* Variation name */}
              </div>
              <div className="flex flex-col items-center">
                <Skeleton className="h-32 w-full" />
                {/* Product variation image */}
                <Skeleton className="h-6 w-32 mt-2" /> {/* Variation name */}
              </div>
              <div className="flex flex-col items-center">
                <Skeleton className="h-32 w-full" />
                {/* Product variation image */}
                <Skeleton className="h-6 w-32 mt-2" /> {/* Variation name */}
              </div>
              <div className="flex flex-col items-center">
                <Skeleton className="h-32 w-full" />
                {/* Product variation image */}
                <Skeleton className="h-6 w-32 mt-2" /> {/* Variation name */}
              </div>
            </div>
          </div>
        </div>
      </DashboardListsContainerSkeleton>

      <DashboardListsContainerSkeleton>
        {/* keywords & SEO Settings Section */}
        <div className="w-full flex flex-col space-y-4">
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* keywords */}
            <Skeleton className="h-9 w-full" /> {/* Add keywords input */}
            <div className="flex gap-2 mt-2">
              <Skeleton className="h-8 w-24" /> {/* Tag chip */}
              <Skeleton className="h-8 w-24" /> {/* Tag chip */}
              <Skeleton className="h-8 w-32" /> {/* Tag chip */}
            </div>
          </div>
          <div className="w-full flex flex-col space-y-1">
            <Skeleton className="h-6 w-32" /> {/* SEO Settings */}
            <div className="w-full flex flex-col space-y-1">
              <Skeleton className="h-6 w-24" /> {/* Meta-Title */}
              <Skeleton className="h-9 w-full" />
            </div>
            <div className="w-full flex flex-col space-y-1">
              <Skeleton className="h-6 w-32" /> {/* Meta-Description */}
              <Skeleton className="h-9 w-full" />
            </div>
          </div>
        </div>
      </DashboardListsContainerSkeleton>
    </div>
  );
}
